threadpool {
  poolSize = 50
}

server {
  port = 5001
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 90%
    non_heap.used_max_percentage = "<80.0" //should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

security.hmac {
  secret.key = """ENC(yFg9jaVtVxRq4ZJ4Brnqcf18F2alPXxAhN6YbtqV0ZxYuQLnFcUgHEWSQ40c2RyPFRFjpjkSYb0a)"""
  ttl = 5
  time.interval = 5
  strength = 512
}

cors {
  allowedDomains = [
    "http://swagger.us-east-vpc.socure.be"
  ]
}

jmx {
  port = 1098
}

match.weighting {
  useControlCenter = false
  controlCentreTimeout = 5
  weights {
    default-match-weights {
      firstNameWeight = 4.0,
      lastNameWeight = 9.0,
      ssnWeight = 10.0,
      ssn4Weight = 5.0,
      dobWeight = 6.0,
      phoneWeight = 1.0,
      emailWeight = 1.0,
      streetAddressWeight = 4.0,
      cityWeight = 3.0,
      zipWeight = 2.0,
      stateWeight = 1.0
    }
    challenger-match-weights {
      firstNameWeight = 5.0,
      lastNameWeight = 4.0,
      ssnWeight = 9.0,
      ssn4Weight = 4.0,
      dobWeight = 9.0,
      phoneWeight = 1.0,
      emailWeight = 1.0,
      streetAddressWeight = 3.0,
      cityWeight = 2.0,
      zipWeight = 2.0,
      stateWeight = 7.0
    }
  }
}

### Remove below 2 once fix for above is deployed

default-match-weights {
  firstNameWeight=4.0,
  lastNameWeight=9.0,
  ssnWeight=10.0,
  dobWeight=6.0,
  phoneWeight=4.0,
  streetAddressWeight=4.0,
  cityWeight=3.0,
  zipWeight=2.0,
  stateWeight=1.0
}

challenger-match-weights {
  firstNameWeight=5.0,
  lastNameWeight=4.0,
  ssnWeight=9.0,
  dobWeight=9.0,
  phoneWeight=1.0,
  streetAddressWeight=3.0,
  cityWeight=2.0,
  zipWeight=2.0,
  stateWeight=7.0
}

###########

elastic {
  host = "vpc-kyc-search-dev-efx-2slllhms4z4ibamo76pvr25nfi.us-east-1.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-1"
  indexName = "identities-cid-dev-new,identities-cid-dev-latest-equifax"
  querySize = 100
  dynamo {
    enabled = true
    table = "kyc_identity_data_dev"
  }
}

alternate {
    elastic {
      host = "vpc-kyc-search-dev-efx-2slllhms4z4ibamo76pvr25nfi.us-east-1.es.amazonaws.com"
      port = 443
      protocol = "https"
      region = "us-east-1"
      indexName = "identities-cid-dev-new"
      querySize = 100
      dynamo {
        enabled = true
        table = "kyc_identity_data_dev"
      }
    }
    enf.elastic {
      host = "vpc-kyc-search-dev-efx-2slllhms4z4ibamo76pvr25nfi.us-east-1.es.amazonaws.com"
      port = 443
      protocol = "https"
      region = "us-east-1"
      indexName = "identities-cid-dev-new"
      querySize = 10
      dynamo {
        enabled = true
        table = "kyc_identity_data_dev"
      }
    }
    bld.elastic {
      enabled = true
      host = "vpc-kyc-search-dev-efx-2slllhms4z4ibamo76pvr25nfi.us-east-1.es.amazonaws.com"
      port = 443
      protocol = "https"
      region = "us-east-1"
      indexName = "identities-cid-bold-dev"
      querySize = 10
      dynamo {
        enabled = true
        table = "kyc_identity_bold_data_dev"
      }
    }
}

enf.elastic {
  host = "vpc-kyc-search-dev-efx-2slllhms4z4ibamo76pvr25nfi.us-east-1.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-1"
  indexName = "identities-cid-dev-new,identities-cid-dev-latest-enformion"
  querySize = 10
  dynamo {
    enabled = true
    table = "kyc_identity_data_dev"
  }
}

bld.elastic {
  enabled = true
  host = "vpc-kyc-search-dev-efx-2slllhms4z4ibamo76pvr25nfi.us-east-1.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-1"
  indexName = "identities-cid-bold-dev,identities-cid-dev-latest-bold"
  querySize = 10
  dynamo {
    enabled = true
    table = "kyc_identity_bold_data_dev"
  }
}

obitscrapper.elastic {
  enabled = true
  host =  "vpc-kyc-search-dev-efx-2slllhms4z4ibamo76pvr25nfi.us-east-1.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-1"
  indexName = "identities-cid-deceased-data"
  querySize = 10
  timeoutInMillis = -1
  dynamo {
    enabled = true
    table = "kyc_identity_data_dev"
  }
}

docv.datasource {
  elastic {
    enabled = true
    host = "vpc-kyc-search-dev-efx-2slllhms4z4ibamo76pvr25nfi.us-east-1.es.amazonaws.com"
    port = 443
    protocol = "https"
    region = "us-east-1"
    indexName = "identities-cid-dev"
    querySize = 15
    timeoutInMillis = 150
  }
}

ssn.dynamodb {
  table = "kyc_identity_data_ssn_tbl_unified"
  timeout = 800
  correctionlog = {
    enabled = true
    table = "kyc_ssn_corrections_tbl_dev"
  }
}

#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "idplus-global-settings-************-us-east-1"
  }
  memcached {
    host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
    port=11211
    ttl="24 hours"
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Control Center==========================#

enformiom.merge.suppressed {
  accountIds = []
}

#==================== SQS for Display BME in Admin Dashboard ==========================#

sqs {
  primary {
    region = "us-east-1",
    queue = "transaction-resolved-entity-worker-dev-be2a1252"
  }
  secondary {
    region = "us-west-2",
    queue = "transaction-resolved-entity-worker-dev-be2a1252"
  }
  s3.fallback {
    region = "us-east-1",
    bucket = "transaction-resolved-entity-dev-************-us-east-1",
    basePath = "BestMatchEntity",
    kmsId = "arn:aws:kms:us-east-1:************:key/7be1b782-93b8-437d-81fb-da11d61e9af7"
  }
  retry {
    initial.backoff = "1 seconds",
    max.backoff = "32 seconds",
    multiplier = 2,
    max.attempts = 5
  }
}

mock.s3.bucketName = "kyc-mock-testdata-dev-************-us-east-1"

memcached {
  host = "product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
  port = 11211
}

#========== Account Service ============#
account.service {
  endpoint = "https://account-service.webapps.us-east-1.product-dev.socure.link"
  region = "us-east-1"
  hmac {
    secret.key = """ENC(N+wQ3y/sExAmQyNbB2RPwiJNZgXErpT8S9Ia58Jtau7XA28qnXDcUQqAqdQNotPmYcOE5o6lHYT3dkQM6FBcMA==)"""
    strength = 512
    realm = "Socure"
    version = "1.0"
  }
}
#========== Account Service ============#

ecbsv.inhouse {
  enabled = true
  timeout = 200 // In milliseconds
  db = {
    fetch.limit = 10
    useLocalDB = false
    retry {
      interval = "10 milliseconds"
      attempts = 2
    }
    database {
      user = "rds-txn-resolved-entity-store-dev-c12476-app"
      driver = com.amazonaws.secretsmanager.sql.AWSSecretsManagerPostgreSQLDriver
      jdbcUrl = "jdbc-secretsmanager:postgresql://txn-resolved-entity-store-dev.cluster-ro-c8dcsxvsf1es.us-east-1.rds.amazonaws.com:5432/ecbsv_db"
      dataSourceName = "ecbsv-db-reader"
      maxIdleTime = 900
      maxConnectionAge = 3600
      maxPoolSize = 50
      minPoolSize = 3
      initialPoolSize = 3
      testConnectionOnCheckIn = true
      testConnectionOnCheckOut = false
      idleConnectionTestPeriod = 20
    }
  }
}

socureID {
  endpoint = "https://id-resolution.webapps.us-east-1.ds.socure.link/search/kyc-entity"
}

dynamic.control.center {
  s3 {
    bucketName = "globalconfig-************-us-east-1"
  }
  memcached {
    host = "product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
    port = 11211
    ttl = 86400
  }
  local {
    cache.timeout.minutes = 2
    cache.read.timeout.milliseconds = 60000
  }
}

merge.entity {
  enabled = true
  limit {
    overall = 20
    vendor = 15
  }
}


#=====Deprecated (Does not start if config not present. Need to clean up)=====#
unified.entity {
  elastic {
    enabled = false
    host="vpc-kyc-search-dev-unified-io4v22psakfjzh7extqb22rlmm.us-east-1.es.amazonaws.com"
    port=443
    protocol="https"
    region="us-east-1"
    indexName="identities-cid-dev"
    querySize=10
  }
  sqs {
    messagePush = false
    primary {
      region = "us-west-2",
      queue = "transaction-resolved-entity-worker-dev-be2a1252"
    }
    secondary {
      region = "us-east-1",
      queue = "transaction-resolved-entity-worker-dev-be2a1252"
    }
    s3.fallback {
      region = "us-east-1",
      bucket = "transaction-resolved-entity-dev-************-us-east-1-be2a1252",
      basePath = "UnifiedEntity",
      kmsId = "arn:aws:kms:us-east-1:************:key/7be1b782-93b8-437d-81fb-da11d61e9af7"
    }
    retry {
      initial.backoff = "1 seconds",
      max.backoff = "32 seconds",
      multiplier = 2,
      max.attempts = 5
    }
  }
}

#===================================================================================================#

unique.id {
  sqs.push.enabled = true
  retry {
    interval = "10 milliseconds"
    attempts = 2
  }
  dynamodb {
    tableName = "kyc_entity_unification_dev"
  }
  timeout.generate = "500 milliseconds"
}

anchor.enabled=true
dynamo.enabled=true
dynamo.kyc.config.table_name=kyc_table_config_dev
dynamo {
  scheduler {
    poolsize = 2
    interval.seconds = 120
  }
  region = "us-east-1"
  maxConcurrency = 100
}

server.metrics.enabled = false

internal_entity {
  elastic {
    host = "vpc-kyc-search-dev-efx-2slllhms4z4ibamo76pvr25nfi.us-east-1.es.amazonaws.com"
    port = 443
    protocol = "https"
    region = "us-east-1"
    indexName = "identities-cid-internal-entity"
    querySize = 15
  }
  timeout = 1000
  enabled = true
  dynamo.table = "kyc_internal_entity_dev"
}

deceasedConfigName = "elasticSearchConnectionsConf"

show {
  top20obitRecords = true
  nationalIdQueryResults = true
  additonalMatches = true
  top20EquifaxEntities = true
  top20EnformionEntities = true
}

#================ Smarty Streets ================#

smartystreets.service {
  endpoint = "https://smartystreets.webapps.us-east-1.product-dev.socure.link"
}

#================ Smarty Streets ================#

#================ Transaction Auditing ==========#

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region=us-east-1
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-dev-************-us-east-1"
      }
      third-party {
        region=us-east-1
        bucket="sqs-storage-dev-************-us-east-1"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

#================ Transaction Auditing ==========#

inhouse {
  dynamo {
    region = "us-east-1"
    concurrency = 100
    table = "ecbsv_inhouse_data_dev"
    index = "ecbsv_inhouse_data_gsi_ssn_hmac_created_at"
    hmacSecret = "ENC(nKwBkT8j5jbEqDT0xgSP+Bp7U7hwAHZsgGeUlOTODnNmyUa957siGze52VizT72qAs2IUg==)"
  }
}

rulecode {
  configTable = "rc_config_dev"
  currentConfigKey = "rc_table_config"
  table.refresh.enabled = true
  scheduler {
    poolsize = 2
    interval.seconds = 120
  }
  tower_data_table_name = {
    "table_name" : "towerdata_email_lookup",
    "email_first_seen_path" : "eam.date_first_seen",
    "is_data_compressed" : true
  }
  full_contact_table_name = {
    "table_name" : "fullcontact_email_lookup",
    "email_first_seen_path" : "persons.emailFirstSeen",
    "email_last_seen_path" : "persons.emailLastSeen",
    "is_data_compressed" : true
  }
  production_data_table_name = {
    "table_name" : "sv4_correlation_features",
    "email_first_seen_path" : "fsd",
    "email_last_seen_path" : "lsd",
    "is_data_compressed" : false
  }
}

email.processing.enabled = true

#================ Reasoncode Service config ================#
reasoncode.service {
  endpoint="https://reasoncode.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://reasoncode.webapps.us-east-1.product-dev.socure.link"
  hmac {
    secret.key="""ENC(tDQyn4rBrAqmMSAO9GTDWGk6nRJnZTceGIyOEqvcorqnQ7lG0AIPxoHQ/9u4pMHAReCPtzz+2Psc577Su4Smuw==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
  metrics.enabled = false
}