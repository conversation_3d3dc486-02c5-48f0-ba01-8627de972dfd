package me.socure.common.kyc.model.es.result

import me.socure.common.kyc.model.{City, CustomKYCPreferences, DOB, FirstName, KYCPreferences, KycEntitySearchRequest, MiddleName, MobileNumber, NationalId, State, StreetAddress, SurName, Workflows, ZipCode}
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization
import org.scalatest.{FreeSpec, Matchers}

import scala.util.control.NonFatal

class DOBSSNIssuanceCleanupTest extends FreeSpec with Matchers {

  private val sampleRecords = Records(
    firstName = Array("<PERSON>", "<PERSON>"),
    middleName = Array("A.", "B."),
    surName = Array("Doe", "Smith"),
    ssn = Array("123456789", "987654321"),
    dob = Array("19800101", "19900515"),
    mobileNumber = Array("5551234567", "5557654321"),
    streetAddress = Array("123 Elm St", "456 Oak Ave"),
    city = Array("Anytown", "Sometown"),
    zipCode = Array("12345", "54321"),
    state = Array("CA", "NY"),
    ssnDeceased = Array("0", "1"),
    cidDeceased = Array("1", "0"),
    invalidSSN = Array("1", "0"),
    ssnYearHigh = Array("2000", "2010"),
    ssnYearLow = Array("1980", "1990"),
    ssnConfirm = Array("1", "1"),
    factaCode = Array.empty,
    ciRowId = Array("CID123", "CID456"),
    aRowId = Array("AID789", "AID101"),
    addressType = Array("Residential", "Commercial"),
    rowIds = Array("RID1", "RID2"),
    clusterId = Some("Cluster123"),
    suffixName = Array("Jr.", "III"),
    phoneFirstSeen = Array("20000101", "20050101"),
    phoneLastSeen = Array("20200101", "20201231"),
    addressFirstSeen = Array("20000101", "20050101"),
    deceasedDate = Array.empty,
    piiRowIDs = PIIRowIDs(
      firstName = Array("PII123", "PII456"),
      surName = Array("PII123", "PII456"),
      mobileNumber = Array("PII123", "PII456"),
      dob = Array("PII123", "PII456")
    ),
    emailAddress = Array("<EMAIL>", "<EMAIL>"),
    emailFirstSeen = Array("20100101", "20120101"),
    emailLastSeen = Array("20200101", "20210101"),
    ssnIssued = Array("1980", "1990")
  )

  private val request = KycEntitySearchRequest(
    firstName = FirstName("ashley"),
    middleName = Some(MiddleName("N")),
    surName = SurName("paul"),
    streetAddress = Some(StreetAddress("602 ne bush prairie private se unit 12345")),
    city = Some(City("youngville")),
    zipCode = Some(ZipCode("72100")),
    zip4 = None,
    latitude = None,
    longitude = None,
    state = Some(State("la")),
    mobileNumber = Some(MobileNumber("**********")),
    nationalId = Some(NationalId("796040440")),
    dob = Some(DOB("20030208")),
    driverLicense = None,
    preferencesKyc = KYCPreferences(
      exactDob = true,
      exactSSN = true,
      dobMatchLogic = Some("exact_yyyy_mm_dd")
    ),
    customPreferencesKyc = CustomKYCPreferences(
      customDobMatchLogic = None,
      maxAddressCount = None,
      customNameMatching = None,
      maxEmailCount = None,
      maxPhoneCount = None
    ),
    preferencesEntity = KYCPreferences(
      exactDob = true,
      exactSSN = true,
      dobMatchLogic = Some("exact_yyyy_mm_dd")
    ),
    maskPii = false,
    workflows = Workflows.All,
    modulesEnabled = Set("KYC", "ModuleDecisioning")
  )

  implicit val formats: DefaultFormats.type = DefaultFormats

  private def convertToIdentityRecord(record: Records): IdentityRecord = {
    def asOption[T](r: => T): Option[T] = {
      try {
        Some(r)
      } catch {
        case NonFatal(_) => None
      }
    }

    IdentityRecord(
      firstName = record.firstName.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.firstName(index))
        )
      },
      surName = record.surName.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.surName(index))
        )
      },
      middleName = record.middleName.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.surName(index))
        )
      },
      suffixName = record.suffixName.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.surName(index))
        )
      },
      dob = record.dob.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.dob(index))
        )
      },
      phoneNumber = record.mobileNumber.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.mobileNumber(index)),
          firstSeen = asOption(record.phoneFirstSeen(index)),
          lastSeen = asOption(record.phoneFirstSeen(index))
        )
      },
      email = record.emailAddress.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.mobileNumber(index)),
          firstSeen = asOption(record.emailFirstSeen(index)),
          lastSeen = asOption(record.emailLastSeen(index))
        )
      },
      ssn = record.ssn.zipWithIndex.map {
        case (value, index) => SSNField(
          value = value,
          rowId = asOption(record.ciRowId(index)),
          ssnConfirm = asOption(record.ssnConfirm(index)),
          invalid = asOption(record.invalidSSN(index)),
          ssnYearHigh = asOption(record.ssnYearHigh(index)),
          cidDeceased = asOption(record.cidDeceased(index)),
          ssnYearLow = asOption(record.ssnIssued(index)),
          ssnDeceased = asOption(record.ssnDeceased(index)),
          factaCode = asOption(record.factaCode(index)),
          dod = asOption(record.deceasedDate(index))
        )
      },
      address = record.streetAddress.zipWithIndex.map {
        case (value, index) => AddressField(
          street = value,
          city = asOption(record.city(index)).getOrElse(""),
          state = asOption(record.state(index)).getOrElse(""),
          zipCode = asOption(record.zipCode(index)).getOrElse(""),
          rowId = asOption(record.aRowId(index)),
          firstSeen = asOption(record.addressFirstSeen(index)),
          lastSeen = None,
          addressCommercial = asOption(record.addressType(index))
        )
      },
      clusterId = record.clusterId.toSeq,
      allAssociatedSSNs = record.ssn.zipWithIndex.map {
        case (value, index) => SSNField(
          value = value,
          rowId = asOption(record.ciRowId(index)),
          ssnConfirm = asOption(record.ssnConfirm(index)),
          invalid = asOption(record.invalidSSN(index)),
          ssnYearHigh = asOption(record.ssnYearHigh(index)),
          cidDeceased = asOption(record.cidDeceased(index)),
          ssnYearLow = asOption(record.ssnIssued(index)),
          ssnDeceased = asOption(record.ssnDeceased(index)),
          factaCode = asOption(record.factaCode(index)),
          dod = asOption(record.deceasedDate(index))
        )
      }
    )
  }

  "DOBSSNIssuanceCleanup Tests" - {
    "should not filter when only one DOB is present" in {
      val records = sampleRecords.copy(
        dob = Array("19850101"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII123"))
      )
      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      actual.updated shouldBe false
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
    }

    "should filter DOBs within SSN issuance range (priority 1)" in {
      val records = sampleRecords.copy(
        dob = Array("19750101", "19850101", "19950101", "20050101"), // 1975, 1985, 1995, 2005
        ssn = Array("123456789"), // SSN with range 1980-2000
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("2000"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3", "PII4"))
      )

      val expected = records.copy(
        dob = Array("19850101", "19950101"), // Within range 1980-2001 (high+1), 2005 is outside
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII2", "PII3"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedDOBs = identityRecord.dob.filter(dob =>
        dob.value.equals("19750101") || dob.value.equals("20050101")
      )
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(dob = removedDOBs)
      )

      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      actual.updated shouldBe true
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "should select closest DOB when all DOBs are prior to SSN range (priority 2)" in {
      val records = sampleRecords.copy(
        dob = Array("19700101", "19750101", "19770101"), // All before 1980
        ssn = Array("123456789"),
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("2000"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3"))
      )

      val expected = records.copy(
        dob = Array("19770101"), // Closest to 1980
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII3"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedDOBs = identityRecord.dob.filter(dob =>
        dob.value.equals("19700101") || dob.value.equals("19750101")
      )
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(dob = removedDOBs)
      )

      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      actual.updated shouldBe true
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "should select closest DOB when all DOBs are after SSN range (priority 3)" in {
      val records = sampleRecords.copy(
        dob = Array("20050101", "20100101", "20150101"), // All after 2001 (2000+1)
        ssn = Array("123456789"),
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("2000"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3"))
      )

      val expected = records.copy(
        dob = Array("20050101"), // Closest to 2001
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII1"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedDOBs = identityRecord.dob.filter(dob =>
        dob.value.equals("20100101") || dob.value.equals("20150101")
      )
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(dob = removedDOBs)
      )

      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      actual.updated shouldBe true
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "should prefer ±1 range when SSN low year equals high year" in {
      val records = sampleRecords.copy(
        dob = Array("19790101", "19850101"), // 1979, 1985 - no DOB within normal range
        ssn = Array("123456789"),
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("1980"), // Same year
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2"))
      )

      val expected = records.copy(
        dob = Array("19790101"), // Within ±1 range (1979-1981), 1985 is outside
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII1"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedDOB = identityRecord.dob.find(_.value.equals("19850101")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(dob = Seq(removedDOB))
      )

      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      actual.updated shouldBe true
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "should handle multiple SSNs with different issuance ranges" in {
      val records = sampleRecords.copy(
        dob = Array("19750101", "19850101", "19950101", "20050101"), // 1975, 1985, 1995, 2005
        ssn = Array("123456789", "987654321"),
        ssnYearLow = Array("1980", "1990"), // Overall range: 1980-2000
        ssnYearHigh = Array("1990", "2000"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3", "PII4"))
      )

      val expected = records.copy(
        dob = Array("19850101", "19950101"), // Within range 1980-2001 (high+1), 2005 is outside
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII2", "PII3"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedDOBs = identityRecord.dob.filter(dob =>
        dob.value.equals("19750101") || dob.value.equals("20050101")
      )
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(dob = removedDOBs)
      )

      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      actual.updated shouldBe true
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "should not filter when no SSN issuance data is available" in {
      val records = sampleRecords.copy(
        dob = Array("19750101", "19850101", "19950101"),
        ssn = Array("123456789"),
        ssnYearLow = Array(""), // Empty issuance data
        ssnYearHigh = Array(""),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      actual.updated shouldBe false
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
    }

    "should handle mixed SSN data (some with issuance data, some without)" in {
      val records = sampleRecords.copy(
        dob = Array("19750101", "19850101", "19950101"), // 1975, 1985, 1995
        ssn = Array("123456789", "987654321"),
        ssnYearLow = Array("1980", ""), // Only first SSN has issuance data
        ssnYearHigh = Array("1990", ""),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3"))
      )

      val expected = records.copy(
        dob = Array("19850101"), // Within range 1980-1991 (high+1), 1995 is outside
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII2"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedDOBs = identityRecord.dob.filter(dob =>
        dob.value.equals("19750101") || dob.value.equals("19950101")
      )
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(dob = removedDOBs)
      )

      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      actual.updated shouldBe true
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "should return all DOBs when no clear selection criteria is met" in {
      val records = sampleRecords.copy(
        dob = Array("19750101", "20050101"), // Some before, some after range
        ssn = Array("123456789"),
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("1990"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      // Should not filter when there's a mix and no clear priority
      actual.updated shouldBe false
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
    }

    "should handle SSN lookup when ssnYearLow/High are not provided" in {
      val records = sampleRecords.copy(
        dob = Array("19750101", "19850101", "19950101"),
        ssn = Array("532081234"), // This should trigger SSN lookup
        ssnYearLow = Array(""), // Empty, should use SSN lookup
        ssnYearHigh = Array(""),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      // The behavior depends on what SSNUtil.ssnLookup returns for this SSN
      // This test verifies the lookup mechanism works without throwing exceptions
      actual.records.dob.length should be <= records.dob.length
    }

    "should handle invalid DOB formats gracefully" in {
      val records = sampleRecords.copy(
        dob = Array("invalid", "19850101", ""), // Invalid and empty DOBs
        ssn = Array("123456789"),
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("1990"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      // Should handle invalid DOBs gracefully and only process valid ones
      actual.records.dob should contain("19850101")
    }

    "should handle empty SSN arrays" in {
      val records = sampleRecords.copy(
        dob = Array("19750101", "19850101"),
        ssn = Array.empty[String], // No SSNs
        ssnYearLow = Array.empty[String],
        ssnYearHigh = Array.empty[String],
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      // Should not filter when no SSN data is available
      actual.updated shouldBe false
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
    }

    "should handle array index mismatch gracefully" in {
      val records = sampleRecords.copy(
        dob = Array("19750101", "19850101", "19950101"), // 3 DOBs
        ssn = Array("123456789"),
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("2000"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2")) // Only 2 piiRowIDs - mismatch!
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      // Should handle the mismatch gracefully without throwing exceptions
      actual.records.dob.length should be <= records.dob.length
      actual.records.piiRowIDs.dob.length should be <= records.piiRowIDs.dob.length
      // Should not crash due to array bounds issues
      actual.updated shouldBe true
    }

    "should handle empty SSN arrays gracefully" in {
      val records = sampleRecords.copy(
        dob = Array("19750101", "19850101"),
        ssn = Array.empty[String], // No SSNs
        ssnYearLow = Array.empty[String],
        ssnYearHigh = Array.empty[String],
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      // Should not filter when no SSN data is available
      actual.updated shouldBe false
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
    }

    "should handle distinctBy logic correctly for duplicate DOB values" in {
      val records = sampleRecords.copy(
        dob = Array("19850101", "19850101", "19950101"), // Duplicate DOB values but different indices
        ssn = Array("123456789"),
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("2000"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      // Should handle duplicates correctly using distinctBy index
      actual.records.dob.length should be <= records.dob.length
      actual.records.piiRowIDs.dob.length shouldBe actual.records.dob.length
    }

    "should handle mixed scenario fallback logic when DOBs are on both sides of range" in {
      val records = sampleRecords.copy(
        dob = Array("19650101", "19700101", "20100101", "20150101"), // Multiple DOBs on both sides of range (1980-2000)
        ssn = Array("123456789"),
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("2000"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3", "PII4"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      // Should apply mixed scenario fallback logic - select closest DOBs from both sides
      actual.updated shouldBe true // Filtering occurs: 4 DOBs -> 2 DOBs
      actual.records.dob.length shouldBe 2 // Should select closest from each side
      actual.records.dob should contain("19700101") // Closest to low range (1980)
      actual.records.dob should contain("20100101") // Closest to high range (2001)
    }

    "should handle all DOBs prior to range scenario" in {
      val records = sampleRecords.copy(
        dob = Array("19650101", "19700101", "19750101"), // All before range (1980-2000)
        ssn = Array("123456789"),
        ssnYearLow = Array("1980"),
        ssnYearHigh = Array("2000"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII1", "PII2", "PII3"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val actual = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))

      // Should select closest to low year (1980)
      actual.updated shouldBe true
      actual.records.dob.length shouldBe 1
      actual.records.dob should contain("19750101") // Closest to 1980
    }
  }
}
